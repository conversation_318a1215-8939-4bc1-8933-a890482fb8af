# GoLogin Custom Functions

Tính năng mở browser từ profile folder dưới local sử dụng GoLogin API.

## 🚀 Tính năng

- **Mở browser từ local profile**: Launch browser từ folder profile đã tải về
- **L<PERSON>y danh sách profiles local**: Scan và lấy thông tin tất cả profiles trong folder
- **Quản lý browser**: Đóng browser và dọn dẹp tài nguyên
- **Đọc metadata**: Tự động đọc notes và proxy từ file txt

## 📁 Cấu trúc Profile

Profiles được lưu trong folder với cấu trúc:
```
gologin_profiles/
├── gologin_profile_PROFILE_ID_1/
│   ├── chrome_data/
│   ├── note.txt          # Ghi chú profile (tùy chọn)
│   ├── proxy.txt         # Thông tin proxy (tùy chọn)
│   └── ... (chrome profile files)
├── gologin_profile_PROFILE_ID_2/
└── ...
```

## 🔧 Cài đặt

Đả<PERSON> bả<PERSON> đã cài đặt dependencies:
```bash
npm install puppeteer-core
```

## 📖 Cách sử dụng

### 1. Import functions

```javascript
import {
    launchBrowserFromLocal,
    getLocalProfiles,
    closeBrowser
} from './custom-function.js';
```

### 2. Lấy danh sách profiles local

```javascript
const profiles = getLocalProfiles('./gologin_profiles');
console.log('Local profiles:', profiles);
```

### 3. Mở browser từ profile local

```javascript
const { browser } = await launchBrowserFromLocal({
  token: 'YOUR_GOLOGIN_API_TOKEN',
  profileId: 'PROFILE_ID',
  profilesDir: './gologin_profiles'
});

// Sử dụng browser
const page = await browser.newPage();
await page.goto('https://google.com');
```

### 4. Đóng browser

```javascript
await closeBrowser(browser);
```

## 🎯 Ví dụ hoàn chỉnh

```javascript
import {launchBrowserFromLocal, getLocalProfiles, closeBrowser} from './custom-function.js';

async function example() {
    try {
        // Lấy danh sách profiles
        const profiles = getLocalProfiles();
        console.log('Available profiles:', profiles);

        if (profiles.length === 0) {
            console.log('No profiles found');
            return;
        }

        // Mở browser cho profile đầu tiên
        const {browser} = await launchBrowserFromLocal({
            token: 'YOUR_TOKEN_HERE',
            profileId: profiles[0].id,
            profilesDir: './gologin_profiles'
        });

        // Tạo page và navigate
        const page = await browser.newPage();
        await page.goto('https://httpbin.org/ip');

        // Lấy thông tin IP
        const ipInfo = await page.evaluate(() => {
            return JSON.parse(document.body.innerText);
        });
        console.log('Current IP:', ipInfo.origin);

        // Đóng browser
        await closeBrowser(browser);

    } catch (error) {
        console.error('Error:', error.message);
    }
}

example();
```

## 🎮 Chạy demo

```bash
node example-local-browser.js
```

## ⚙️ Tùy chọn cấu hình

### launchBrowserFromLocal options:

| Option | Type | Required | Default | Description |
|--------|------|----------|---------|-------------|
| `token` | string | ✅ | - | GoLogin API token |
| `profileId` | string | ✅ | - | Profile ID cần mở |
| `profilesDir` | string | ❌ | `'./gologin_profiles'` | Thư mục chứa profiles |
| `executablePath` | string | ❌ | - | Đường dẫn Chrome executable |

### getLocalProfiles options:

| Option | Type | Required | Default | Description |
|--------|------|----------|---------|-------------|
| `profilesDir` | string | ❌ | `'./gologin_profiles'` | Thư mục chứa profiles |

## 📋 Response Format

### launchBrowserFromLocal response:
```javascript
{
  browser: PuppeteerBrowser,    // Browser instance
  profileId: string,            // Profile ID
  profileFolder: string,        // Đường dẫn folder profile
  success: true                 // Trạng thái thành công
}
```

### getLocalProfiles response:
```javascript
[
  {
    id: "PROFILE_ID",           // Profile ID
    name: "Profile Name",       // Tên profile
    path: "/path/to/profile",   // Đường dẫn folder
    status: "ready",            // Trạng thái
    notes: "Profile notes",     // Ghi chú (nếu có)
    proxy: "proxy:port"         // Proxy (nếu có)
  }
]
```

## 🚨 Lỗi phổ biến

### 1. Profile folder not found
```
Error: Profile folder not found: ./gologin_profiles/gologin_profile_XXXXX
```
**Giải pháp**: Đảm bảo profile đã được tải về và folder tồn tại.

### 2. GoLogin API token is required
```
Error: GoLogin API token is required
```
**Giải pháp**: Cung cấp token hợp lệ từ GoLogin dashboard.

### 3. Profiles directory not found
```
Error: Profiles directory not found: ./gologin_profiles
```
**Giải pháp**: Tạo thư mục profiles hoặc chỉ định đúng đường dẫn.

## 🔗 Integration với Desktop App

Để tích hợp với desktop app:

```javascript
// desktop-app/preload.js
import { launchBrowserFromLocal, getLocalProfiles } from '../src/custom-function.js';

contextBridge.exposeInMainWorld('electronAPI', {
  // ... existing APIs
  launchLocalBrowser: (options) => launchBrowserFromLocal(options),
  getLocalProfiles: (dir) => getLocalProfiles(dir)
});
```

```javascript
// desktop-app/src/renderer/App.jsx
const handleLaunchProfile = async (profileId) => {
  try {
    const result = await window.electronAPI.launchLocalBrowser({
      token: 'YOUR_TOKEN',
      profileId,
      profilesDir: './gologin_profiles'
    });
    console.log('Browser launched:', result);
  } catch (error) {
    console.error('Launch failed:', error);
  }
};
```

## 📝 Notes

- Đảm bảo có quyền đọc/ghi trong thư mục profiles
- Token API cần có quyền launch profiles
- Browser sẽ sử dụng Chrome profile data từ folder local
- Mỗi profile chỉ nên mở 1 browser instance tại một thời điểm

## 🔧 Troubleshooting

1. **Kiểm tra token**: Đảm bảo token còn hiệu lực
2. **Kiểm tra permissions**: Đảm bảo có quyền đọc folder profiles
3. **Kiểm tra Chrome**: Đảm bảo Chrome/Chromium được cài đặt
4. **Kiểm tra network**: Đảm bảo kết nối internet ổn định
