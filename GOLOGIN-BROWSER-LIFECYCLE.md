## Vòng đời của một trình duyệt antidetect-browser (GoLogin)

### **1. Khởi tạo (Initialization)**
```javascript
const api = GologinApi({ token });
const { browser } = await api.launch({ profileId: "123" });
```
- **Tải profile** từ server hoặc tạo mới
- **<PERSON><PERSON><PERSON> bị cấu hình**: proxy, timezone, fonts, extensions, cookies
- **Spawn Chromium/Orbita** với args cụ thể
- **Mở remote debugging port** để Puppeteer connect

### **2. Ho<PERSON><PERSON> động (Active Session)**
```javascript
const page = await browser.newPage();
await page.goto("https://example.com");
// User tương tác, automation, scraping...
```
- **Browser chạy** với fingerprint đã cấu hình
- **Cookies được lưu** vào SQLite DB local
- **Session data** đư<PERSON><PERSON> ghi vào profile folder

### **3. <PERSON><PERSON><PERSON><PERSON><PERSON> (Session End)**
```javascript
await browser.close();
await api.exit(); // Hoặc gọi riêng lẻ
```

### **4. Cleanup & Update (Sau khi đóng)**

#### **Dữ liệu cần update:**
- **Cookies mới** (login sessions, preferences)
- **Bookmarks** (nếu user thêm/sửa)
- **Local storage** (form data, settings)
- **Cache files** (được clean để giảm dung lượng)

#### **Quy trình update:**

```javascript
// Trong GoLogin.stopAndCommit()
await this.uploadProfileDataToServer(); // Cookies + Bookmarks
await this.commitProfile();             // Toàn bộ profile folder
await this.clearProfileFiles(local);    // Cleanup local
```

### **5. Cơ chế lưu trữ**

#### **Tự động (Browser tự lưu):**
- **Cookies**: Chrome tự động lưu vào SQLite DB
- **Preferences**: Chrome ghi vào `Default/Preferences`
- **Bookmarks**: Chrome ghi vào `Default/Bookmarks`
- **Extensions data**: Chrome lưu vào `Default/Extensions/`

#### **Thủ công (Code xử lý):**
- **Upload cookies** lên server: `uploadProfileDataToServer()`
- **Nén profile folder**: `archiveProfile()` → zip
- **Gửi lên S3**: `postFile()` 
- **Cleanup local**: `sanitizeProfile()` + `clearProfileFiles()`

### **6. Tại sao cần update thủ công?**

1. **Browser chỉ lưu local** - không tự sync lên server
2. **Profile data** cần được backup để dùng lại
3. **Cookies mới** (login sessions) cần được lưu trữ
4. **State consistency** giữa local và server

### **7. Ví dụ flow hoàn chỉnh**

```javascript
// 1. Khởi động
const { browser } = await api.launch({ profileId: "123" });

// 2. Sử dụng
const page = await browser.newPage();
await page.goto("https://login.site.com");
await page.type("#username", "user");
await page.type("#password", "pass");
await page.click("#login");

// 3. Đóng browser
await browser.close();

// 4. Update data (tự động trong api.exit())
await api.exit(); 
// - Upload cookies mới (login session)
// - Commit profile changes
// - Cleanup local files
```

### **Kết luận**

- **Browser tự động lưu** cookies, preferences, bookmarks vào local
- **Code cần xử lý** việc sync data lên server và cleanup
- **Không có auto-sync** - mọi thứ đều thủ công thông qua SDK methods
- **Đây là thiết kế có chủ ý** để user kiểm soát được khi nào update data