## Luồng hoạt động: src/gologin-api.js

### Mục tiêu file
- Cung cấp API cấp cao (factory) để khởi chạy GoLogin profile theo 2 chế độ: Local và Cloud.
- B<PERSON><PERSON> (wrap) lớp low-level `GoLogin` trong `src/gologin.js`, đồng thời trả về đối tượng Puppeteer `browser` sẵn sàng sử dụng.

### Điểm vào chính
- `GologinApi({ token })`: Factory tạo một API object gắn với `token`.
- `api.launch(params)`: Hàm chính để khởi chạy profile.
  - Nhánh `cloud`: `launchCloudProfile(params)`
  - Nhánh `local`: `launchLocal(params)`

### Sơ đồ luồng (Local)

Client → `GologinApi({ token })` → `api.launch(params)`
  → `launchLocal(params)`
    → `createGologinProfileManager({...params, token})` → `new GoLogin(mergedParams)`
    → N<PERSON>u thiếu `profileId` → `quickCreateProfile()` → `setProfileId(id)`
    → `legacyGologin.start()` (low-level):
      → chuẩn bị profile (createZeroProfile hoặc download từ S3), apply prefs/proxy/cookies/extensions...
      → spawn Orbita/Chromium, mở cổng CDP, lấy `wsUrl`
    → `puppeteer.connect({ browserWSEndpoint: wsUrl })`
    → Trả `{ browser }` và lưu vào `browsers[]` (phục vụ `exit()` dọn dẹp)

### Sơ đồ luồng (Cloud)

Client → `GologinApi({ token })` → `api.launch({ cloud: true, profileId })`
  → `launchCloudProfile(params)`
    → Nếu thiếu `profileId` → `quickCreateProfile()` → `setProfileId(id)`
    → Tạo `browserWSEndpoint = https://cloudbrowser.gologin.com/connect?token=...&profile=...`
    → `puppeteer.connect({ browserWSEndpoint })`
    → Trả `{ browser }` và lưu vào `browsers[]`

### Hàm hỗ trợ/tiện ích
- `getDefaultParams()`: Lấy mặc định từ biến môi trường (`GOLOGIN_API_TOKEN`, `GOLOGIN_PROFILE_ID`, `GOLOGIN_EXECUTABLE_PATH`).
- `createGologinProfileManager({ profileId, ...params })`: Hợp nhất defaults + params, ánh xạ `profile_id`, trả `new GoLogin(mergedParams)`.

### Các API khác trên object trả về
- `createProfileWithCustomParams(options)`: Gọi API server tạo profile theo cấu hình tùy biến.
- `refreshProfilesFingerprint(profileIds)`: Làm mới fingerprint nhiều profile.
- `createProfileRandomFingerprint(name)`: Tạo profile mới với fingerprint ngẫu nhiên.
- `updateUserAgentToLatestBrowser(profileIds, workspaceId)`: Cập nhật UA theo phiên bản browser mới.
- `changeProfileProxy(profileId, proxyData)`: Cập nhật proxy cho profile.
- `addGologinProxyToProfile(profileId, countryCode, proxyType?)`: Gán gói proxy GoLogin cho profile (mobile/resident/dataCenter) dựa vào traffic còn lại.
- `addCookiesToProfile(profileId, cookies)`: Đẩy cookies lên server cho profile.
- `deleteProfile(profileId)`: Xóa profile trên server.
- `exit()`: Đóng tất cả `browsers[]` và gọi `gl.stopLocal()`/`gl.stopRemote()` để dọn dẹp.
- `exitAll()` (export): Đóng tất cả API instances đã tạo (gom qua `createdApis`).

### Khác biệt Local vs Cloud
- Local: Hồ sơ (profile) được dựng/đồng bộ ở máy local; trình duyệt chạy cục bộ; Puppeteer nối qua `wsUrl` local.
- Cloud: Không dựng local; nối thẳng lên CloudBrowser qua endpoint `connect` (CDP qua internet).

### Ghi chú vòng đời
- Mỗi lần `launch(...)` trả về một `browser` được push vào `browsers[]` và `GoLogin` instance vào `legacyGls[]`.
- Gọi `api.exit()` sẽ đóng toàn bộ `browser` và yêu cầu mỗi `GoLogin` instance dừng/commit.


