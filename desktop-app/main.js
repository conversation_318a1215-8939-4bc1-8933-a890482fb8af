import { app, BrowserWindow, ipc<PERSON>ain, dialog } from 'electron';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const isDev = process.env.NODE_ENV === 'development';

let mainWindow;
let profilesDirectory = null; // Lưu trữ đường dẫn profiles directory
let appSettings = { apiToken: process.env.GL_API_TOKEN || '' }; // Lưu trữ API token

function createWindow() {
  mainWindow = new BrowserWindow({
    width: 1200,
    height: 800,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      enableRemoteModule: false,
      preload: path.join(__dirname, 'preload.cjs')
    },
    icon: path.join(__dirname, 'assets/icon.png'),
    title: 'GoLogin Desktop App'
  });

  if (isDev) {
    mainWindow.loadURL('http://localhost:5173');
    mainWindow.webContents.openDevTools();
  } else {
    mainWindow.loadFile('dist/renderer/index.html');
  }

  // Debug preload script
  mainWindow.webContents.on('did-finish-load', () => {
    console.log('📄 Page loaded, checking preload script...');
    mainWindow.webContents.executeJavaScript(`
      console.log('🔍 Checking electronAPI:', typeof window.electronAPI);
      console.log('🔍 Available functions:', window.electronAPI ? Object.keys(window.electronAPI) : 'Not available');
    `);
  });

  mainWindow.on('closed', () => {
    mainWindow = null;
  });
}

app.whenReady().then(() => {
  createWindow();

  app.on('activate', () => {
    if (BrowserWindow.getAllWindows().length === 0) {
      createWindow();
    }
  });
});

app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

// IPC Handlers for GoLogin
ipcMain.handle('create-profile', async (event, name) => {
  try {
    // Import GoLogin SDK
    const { GologinApi } = await import('../src/gologin-api.js');
    const token = appSettings.apiToken || process.env.GL_API_TOKEN || '';
    
    const gologin = GologinApi({ token });
    const profile = await gologin.createProfileRandomFingerprint(name || 'Desktop Profile');
    
    return { success: true, profile };
  } catch (error) {
    console.error('Error creating profile:', error);
    return { success: false, error: error.message };
  }
});

// Handler để lấy profiles từ local directory
ipcMain.handle('get-local-profiles', async () => {
  try {
    if (!profilesDirectory) {
      return { success: true, profiles: [], needSelectDirectory: true };
    }

    const { getLocalProfiles } = await import('../src/custom-function/custom-function.js');
    const profiles = getLocalProfiles(profilesDirectory);
    
    console.log(`📋 Found ${profiles.length} profiles in ${profilesDirectory}`);
    return { 
      success: true, 
      profiles, 
      directory: profilesDirectory,
      needSelectDirectory: false 
    };
  } catch (error) {
    console.error('Error getting local profiles:', error);
    return { success: false, error: error.message };
  }
});

// Handler để mở browser từ local profile
ipcMain.handle('launch-local-browser', async (event, profileId) => {
  try {
    console.log('🎯 IPC Handler: launch-local-browser called with profileId:', profileId);
    console.log('🎯 Current profilesDirectory:', profilesDirectory);
    console.log('🎯 API Token available:', !!appSettings.apiToken);
    
    if (!profilesDirectory) {
      return { success: false, error: 'No profiles directory selected' };
    }

    const { launchBrowserFromLocal } = await import('../src/custom-function/custom-function.js');
    console.log('🎯 About to call launchBrowserFromLocal...');
    
    const result = await launchBrowserFromLocal({
      profileId,
      profilesDir: profilesDirectory,
      token: appSettings.apiToken
      // Không truyền executablePath để dùng Orbit mặc định
    });
    
    console.log('🚀 Browser launched successfully for profile:', profileId);
    return { 
      success: true, 
      profileId: result.profileId,
      profileFolder: result.profileFolder,
      message: 'Browser launched successfully'
    };
  } catch (error) {
    console.error('Error launching local browser:', error);
    return { success: false, error: error.message };
  }
});

ipcMain.handle('get-profiles', async () => {
  try {
    // Return mock data for now
    return { success: true, profiles: [] };
  } catch (error) {
    return { success: false, error: error.message };
  }
});

// Settings handlers
ipcMain.handle('get-settings', async () => {
  try {
    return { success: true, settings: appSettings };
  } catch (error) {
    return { success: false, error: error.message };
  }
});

// Fetch server profiles (avoids CORS in renderer during dev)
ipcMain.handle('fetch-server-profiles', async (event, { token }) => {
  try {
    if (!token) return { success: false, error: 'Missing token' };
    const { API_URL, FALLBACK_API_URL } = await import('../src/utils/common.js');
    const { makeRequest } = await import('../src/utils/http.js');
    const url = `${API_URL}/browser/v2`;
    const fallbackUrl = `${FALLBACK_API_URL}/browser/v2`;
    const data = await makeRequest(url, { method: 'GET' }, { token, fallbackUrl });
    return { success: true, data };
  } catch (error) {
    return { success: false, error: error.message };
  }
});

ipcMain.handle('save-settings', async (event, settings) => {
  try {
    appSettings = { ...appSettings, ...(settings || {}) };
    return { success: true, settings: appSettings };
  } catch (error) {
    return { success: false, error: error.message };
  }
});

// Handler để chọn thư mục profiles
ipcMain.handle('select-profiles-directory', async () => {
  try {
    const result = await dialog.showOpenDialog(mainWindow, {
      properties: ['openDirectory'],
      title: 'Chọn thư mục chứa GoLogin Profiles',
      buttonLabel: 'Chọn thư mục'
    });

    if (!result.canceled && result.filePaths.length > 0) {
      profilesDirectory = result.filePaths[0];
      console.log('📁 Selected profiles directory:', profilesDirectory);
      return { success: true, path: profilesDirectory };
    }

    return { success: false, error: 'User cancelled directory selection' };
  } catch (error) {
    console.error('Error selecting directory:', error);
    return { success: false, error: error.message };
  }
});

// Handler để lấy thư mục profiles hiện tại
ipcMain.handle('get-profiles-directory', async () => {
  try {
    if (profilesDirectory) {
      return { success: true, path: profilesDirectory };
    } else {
      return { success: false, error: 'No profiles directory selected' };
    }
  } catch (error) {
    console.error('Error getting profiles directory:', error);
    return { success: false, error: error.message };
  }
});

// Handler để quét thư mục profiles
ipcMain.handle('scan-profiles-directory', async () => {
  try {
    if (!profilesDirectory) {
      return { success: false, error: 'No profiles directory selected' };
    }

    const fs = await import('fs');
    const path = await import('path');
    
    const profiles = [];
    const items = fs.readdirSync(profilesDirectory);
    
    for (const item of items) {
      const itemPath = path.join(profilesDirectory, item);
      const stats = fs.statSync(itemPath);
      
      if (stats.isDirectory() && item.startsWith('gologin_profile_')) {
        profiles.push({
          id: item,
          name: item.replace('gologin_profile_', ''),
          path: itemPath,
          created: stats.birthtime,
          modified: stats.mtime
        });
      }
    }
    
    return { success: true, profiles };
  } catch (error) {
    console.error('Error scanning profiles directory:', error);
    return { success: false, error: error.message };
  }
});

// Handler để mở profile
ipcMain.handle('open-profile', async (event, profileId) => {
  try {
    if (!profilesDirectory) {
      return { success: false, error: 'No profiles directory selected' };
    }

    const profilePath = path.join(profilesDirectory, profileId);
    const fs = await import('fs');
    
    if (!fs.existsSync(profilePath)) {
      return { success: false, error: 'Profile not found' };
    }

    // Here you would integrate with GoLogin to open the profile
    // For now, just return success
    console.log('🚀 Opening profile:', profilePath);
    return { success: true, message: 'Profile opened successfully' };
  } catch (error) {
    console.error('Error opening profile:', error);
    return { success: false, error: error.message };
  }
});

// Handler để xóa profile
ipcMain.handle('delete-profile', async (event, profileId) => {
  try {
    if (!profilesDirectory) {
      return { success: false, error: 'No profiles directory selected' };
    }

    const profilePath = path.join(profilesDirectory, profileId);
    const fs = await import('fs');
    
    if (!fs.existsSync(profilePath)) {
      return { success: false, error: 'Profile not found' };
    }

    // Delete the profile directory
    fs.rmSync(profilePath, { recursive: true, force: true });
    console.log('🗑️ Deleted profile:', profilePath);
    
    return { success: true, message: 'Profile deleted successfully' };
  } catch (error) {
    console.error('Error deleting profile:', error);
    return { success: false, error: error.message };
  }
});

// Handler để tạo profile mới
ipcMain.handle('create-new-profile', async (event, profileData) => {
  try {
    if (!profilesDirectory) {
      return { success: false, error: 'No profiles directory selected' };
    }

    const fs = await import('fs');
    const path = await import('path');
    
    // Generate unique profile ID
    const timestamp = Date.now();
    const profileId = `gologin_profile_${timestamp}`;
    const profilePath = path.join(profilesDirectory, profileId);
    
    // Create profile directory
    fs.mkdirSync(profilePath);
    
    // Create profile metadata file
    const metadata = {
      id: profileId,
      name: profileData.name || 'New Profile',
      created: new Date().toISOString(),
      ...profileData
    };
    
    fs.writeFileSync(
      path.join(profilePath, 'profile.json'),
      JSON.stringify(metadata, null, 2)
    );
    
    console.log('✨ Created new profile:', profilePath);
    return { success: true, profile: metadata };
  } catch (error) {
    console.error('Error creating new profile:', error);
    return { success: false, error: error.message };
  }
});

// Handler để cập nhật profile
ipcMain.handle('update-profile', async (event, { profileId, updates }) => {
  try {
    if (!profilesDirectory) {
      return { success: false, error: 'No profiles directory selected' };
    }

    const profilePath = path.join(profilesDirectory, profileId);
    const metadataPath = path.join(profilePath, 'profile.json');
    const fs = await import('fs');
    
    if (!fs.existsSync(profilePath)) {
      return { success: false, error: 'Profile not found' };
    }

    // Read existing metadata
    let metadata = {};
    if (fs.existsSync(metadataPath)) {
      metadata = JSON.parse(fs.readFileSync(metadataPath, 'utf8'));
    }
    
    // Update metadata
    metadata = { ...metadata, ...updates, updated: new Date().toISOString() };
    
    // Write updated metadata
    fs.writeFileSync(metadataPath, JSON.stringify(metadata, null, 2));
    
    console.log('✏️ Updated profile:', profileId);
    return { success: true, profile: metadata };
  } catch (error) {
    console.error('Error updating profile:', error);
    return { success: false, error: error.message };
  }
});

// Handler để lấy thông tin hệ thống
ipcMain.handle('get-system-info', async () => {
  try {
    const os = await import('os');
    const process = await import('process');
    
    return {
      success: true,
      info: {
        platform: os.platform(),
        arch: os.arch(),
        version: os.version(),
        totalMemory: os.totalmem(),
        freeMemory: os.freemem(),
        nodeVersion: process.version,
        electronVersion: process.versions.electron
      }
    };
  } catch (error) {
    console.error('Error getting system info:', error);
    return { success: false, error: error.message };
  }
});

// Handler để kiểm tra kết nối internet
ipcMain.handle('check-internet-connection', async () => {
  try {
    const https = await import('https');
    
    return new Promise((resolve) => {
      const req = https.request('https://www.google.com', { method: 'HEAD' }, (res) => {
        resolve({ success: true, connected: true, status: res.statusCode });
      });
      
      req.on('error', () => {
        resolve({ success: true, connected: false });
      });
      
      req.setTimeout(5000, () => {
        req.destroy();
        resolve({ success: true, connected: false, error: 'Timeout' });
      });
      
      req.end();
    });
  } catch (error) {
    console.error('Error checking internet connection:', error);
    return { success: false, error: error.message };
  }
});

// Handler để tạo profile mới và mở browser - backend tự generate profileID
ipcMain.handle('create-download-open-profile', async (event, { token, folderDirectory }) => {
  try {
    const { createDownloadAndOpenProfile } = await import('../src/custom-function/custom-function.js');
    
    const result = await createDownloadAndOpenProfile({ 
      token, 
      folderDirectory 
    });
    
    console.log('✅ Profile created and opened:', result);
    return { success: true, ...result };
  } catch (error) {
    console.error('Error in create-download-open-profile:', error);
    return { success: false, error: error.message };
  }
});

// Download profile from server to local directory
ipcMain.handle('download-profile', async (event, { token, profileId, folderDirectory }) => {
  try {
    const { downloadProfileFromServer } = await import('../src/custom-function/custom-function.js');
    const result = await downloadProfileFromServer({ token, profileId, folderDirectory });
    return { success: true, profilePath: result.profilePath };
  } catch (error) {
    return { success: false, error: error.message };
  }
});

// Handler để bring-to-front browser của profile đang chạy
ipcMain.handle('view-local-profile', async (event, { profileId }) => {
  try {
    if (!profilesDirectory) {
      return { success: false, error: 'No profiles directory selected' };
    }

    const { viewLocalProfile } = await import('../src/custom-function/custom-function.js');
    const result = await viewLocalProfile({ profileId });
    return { success: result.success, pages: result.pages, message: result.message };
  } catch (error) {
    console.error('Error viewing local profile:', error);
    return { success: false, error: error.message };
  }
});

// Handler để dừng profile đang chạy
ipcMain.handle('stop-local-profile', async (event, { profileId, killBrowser = true, saveProfile = false }) => {
  try {
    console.log('🛑 IPC Handler: stop-local-profile called with profileId:', profileId);
    console.log('🛑 Current profilesDirectory:', profilesDirectory);
    console.log('🛑 API Token available:', !!appSettings.apiToken);
    
    if (!profilesDirectory) {
      return { success: false, error: 'No profiles directory selected' };
    }

    if (!appSettings.apiToken) {
      return { success: false, error: 'No API token available' };
    }

    const { stopLocalProfile } = await import('../src/custom-function/custom-function.js');
    console.log('🛑 About to call stopLocalProfile...');
    
    const result = await stopLocalProfile({
      profileId,
      token: appSettings.apiToken,
      profilesDir: profilesDirectory,
      killBrowser,
      saveProfile
    });
    
    console.log('🛑 Profile stopped successfully:', result);
    return { 
      success: true, 
      ...result
    };
  } catch (error) {
    console.error('Error stopping local profile:', error);
    return { success: false, error: error.message };
  }
});

// Handler để mở DevTools (chỉ trong development)
if (isDev) {
  ipcMain.handle('open-devtools', () => {
    if (mainWindow) {
      mainWindow.webContents.openDevTools();
      return { success: true };
    }
    return { success: false, error: 'Main window not available' };
  });
}