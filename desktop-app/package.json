{"name": "gologin-desktop-app", "version": "1.0.0", "description": "GoLogin Desktop Application", "main": "main.js", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "electron": "electron .", "electron:dev": "concurrently \"npm run dev\" \"wait-on http://localhost:5173 && electron .\"", "electron:debug": "cross-env NODE_ENV=development electron --inspect=9229 .", "build:electron": "npm run build && electron-builder", "dist": "npm run build && electron-builder --publish=never", "start": "npm run electron:dev"}, "build": {"appId": "com.gologin.desktop", "productName": "GoLogin Desktop", "directories": {"output": "dist-electron"}, "files": ["dist/**/*", "main.js", "preload.cjs", "package.json"], "win": {"target": "nsis", "icon": "assets/icon.ico"}, "mac": {"target": "dmg", "icon": "assets/icon.icns"}, "linux": {"target": "AppImage", "icon": "assets/icon.png"}}, "devDependencies": {"@tailwindcss/vite": "^4.1.12", "@types/node": "^24.3.0", "@vitejs/plugin-react": "^4.2.1", "autoprefixer": "^10.4.21", "concurrently": "^8.2.2", "daisyui": "^5.0.54", "electron": "^28.1.0", "electron-builder": "^24.9.1", "postcss": "^8.5.6", "tailwindcss": "^4.1.12", "vite": "^5.0.8", "wait-on": "^7.2.0"}, "dependencies": {"@radix-ui/react-checkbox": "^1.3.3", "@radix-ui/react-dropdown-menu": "^2.1.16", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tabs": "^1.1.13", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.542.0", "react": "^18.2.0", "react-dom": "^18.2.0", "tailwind-merge": "^3.3.1", "tailwindcss-animate": "^1.0.7", "tw-animate-css": "^1.3.7"}}