const { contextBridge, ipc<PERSON>enderer } = require('electron');

console.log('🔄 Preload script starting...');

// Expose protected methods that allow the renderer process to use
// the ipcRenderer without exposing the entire object
contextBridge.exposeInMainWorld('electronAPI', {
  // Profile management
  createProfile: (name) => ipcRenderer.invoke('create-profile', name),
  getProfiles: () => ipcRenderer.invoke('get-profiles'),
  getLocalProfiles: () => ipcRenderer.invoke('get-local-profiles'),
  scanProfilesDirectory: () => ipcRenderer.invoke('scan-profiles-directory'),
  deleteProfile: (id) => ipcRenderer.invoke('delete-profile', id),
  createNewProfile: (profileData) => ipcRenderer.invoke('create-new-profile', profileData),
  updateProfile: (profileId, updates) => ipcRenderer.invoke('update-profile', { profileId, updates }),
  
  // <PERSON><PERSON>er control
  openProfile: (profileId) => ipcRenderer.invoke('open-profile', profileId),
  launchLocalBrowser: (profileId) => ipc<PERSON>enderer.invoke('launch-local-browser', profileId),
  viewLocalProfile: (params) => ipcRenderer.invoke('view-local-profile', params),
  stopLocalProfile: (params) => ipcRenderer.invoke('stop-local-profile', params),
  createDownloadOpenProfile: (params) => ipcRenderer.invoke('create-download-open-profile', params),
  
  // Settings
  getSettings: () => ipcRenderer.invoke('get-settings'),
  saveSettings: (settings) => ipcRenderer.invoke('save-settings', settings),
  
  // File operations
  openProfileFolder: (profileId) => ipcRenderer.invoke('open-profile-folder', profileId),
  selectProfilesDirectory: () => ipcRenderer.invoke('select-profiles-directory'),
  getProfilesDirectory: () => ipcRenderer.invoke('get-profiles-directory'),
  
  // System info
  getSystemInfo: () => ipcRenderer.invoke('get-system-info'),
  checkInternetConnection: () => ipcRenderer.invoke('check-internet-connection'),
  
  // Development
  openDevtools: () => ipcRenderer.invoke('open-devtools'),
  
  // Logs
  getLogs: () => ipcRenderer.invoke('get-logs'),
  clearLogs: () => ipcRenderer.invoke('clear-logs')
  ,
  // Server profiles
  fetchServerProfiles: (token) => ipcRenderer.invoke('fetch-server-profiles', { token })
  ,
  // Download single profile
  downloadProfile: (params) => ipcRenderer.invoke('download-profile', params)
});

console.log('✅ Preload script completed, electronAPI exposed');
