import React, { useState, useEffect } from 'react';
import './App.css';
import Header from './components/Header';
import TabContainer from './components/TabContainer';
import DirectorySelector from './components/DirectorySelector';

// Main App Component
function App() {
  const [profiles, setProfiles] = useState([]);
  const [selectedProfile, setSelectedProfile] = useState(null);
  const [loading, setLoading] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [filteredProfiles, setFilteredProfiles] = useState([]);
  const [currentDirectory, setCurrentDirectory] = useState(null);
  const [needSelectDirectory, setNeedSelectDirectory] = useState(false);

  // Filter profiles based on search query
  useEffect(() => {
    if (!searchQuery.trim()) {
      setFilteredProfiles(profiles);
    } else {
      const filtered = profiles.filter(profile =>
        profile.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        profile.notes.toLowerCase().includes(searchQuery.toLowerCase()) ||
        profile.location.toLowerCase().includes(searchQuery.toLowerCase())
      );
      setFilteredProfiles(filtered);
    }
  }, [searchQuery, profiles]);

  // Handle profile launch
  const handleLaunchProfile = async (profileId) => {
    setLoading(true);
    try {
      const result = await window.electronAPI.launchLocalBrowser(profileId);
      if (result.success) {
        console.log('Local browser launched successfully for profile:', profileId);
        console.log('Profile folder:', result.profileFolder);
      } else {
        console.error('Error launching local browser:', result.error);
      }
    } catch (error) {
      console.error('Error:', error.message);
    }
    setLoading(false);
  };

  // Handle view profile
  const handleViewProfile = async (profileId) => {
    try {
      const res = await window.electronAPI.viewLocalProfile({ profileId });
      if (!res?.success) {
        window.alert(res?.message || 'Không tìm thấy browser đang chạy cho profile này');
      }
    } catch (e) {
      window.alert(e.message || 'Lỗi khi View profile');
    }
  };

  // Handle profile stop
  const handleStopProfile = async (profileId) => {
    setLoading(true);
    try {
      const result = await window.electronAPI.stopLocalProfile({
        profileId,
        killBrowser: true,
        saveProfile: false
      });
      
      if (result.success) {
        console.log('✅ Profile stopped successfully:', result);
        window.alert(`Profile ${profileId} đã được dừng thành công!`);
      } else {
        console.error('❌ Error stopping profile:', result.error);
        window.alert(`Lỗi khi dừng profile: ${result.error}`);
      }
    } catch (error) {
      console.error('❌ Error stopping profile:', error.message);
      window.alert(`Lỗi khi dừng profile: ${error.message}`);
    }
    setLoading(false);
  };

  // Handle profile selection
  const handleProfileSelect = (profileId) => {
    setSelectedProfile(profileId);
  };

  // Handle add profile
  const handleAddProfile = () => {
    console.log('Add profile clicked');
    // Add your profile creation logic here
    const newProfile = {
      id: Date.now().toString(),
      name: `Profile ${profiles.length + 1}`,
      state: 'ready',
      notes: '',
      location: '',
      status: 'active'
    };
    setProfiles(prev => [...prev, newProfile]);
  };

  // Handle edit profile
  const handleEditProfile = (profileId) => {
    if (profileId === 'new') {
      handleAddProfile();
    } else {
      console.log('Edit profile:', profileId);
      // Add your profile editing logic here
    }
  };

  // Handle delete profile
  const handleDeleteProfile = (profileId) => {
    console.log('Delete profile:', profileId);
    setProfiles(prev => prev.filter(p => p.id !== profileId));
    if (selectedProfile === profileId) {
      setSelectedProfile(null);
    }
  };

  // Handle buy proxy
  const handleBuyProxy = () => {
    console.log('Buy proxy clicked');
    // Add your proxy purchase logic here
  };

  // Handle menu click
  const handleMenuClick = () => {
    console.log('Menu clicked');
    // Add your menu logic here
  };

  // Handle settings click
  const handleSettingsClick = () => {
    console.log('Settings clicked');
    // Add your settings logic here
  };

  // Handle search
  const handleSearch = (query) => {
    setSearchQuery(query);
  };

  // Handle download all profiles
  const handleDownloadAll = async () => {
    console.log('Download all profiles clicked');
    try {
      // Implement download all logic here
      // For example: download all profiles from GoLogin API
      window.alert('Tính năng Download All đang được phát triển!');
    } catch (error) {
      console.error('Error downloading all profiles:', error);
      window.alert(`Lỗi khi download all: ${error.message}`);
    }
  };

  // Handle fetch profiles from server
  const handleFetchProfiles = async () => {
    console.log('Fetch profiles clicked');
    try {
      // Implement fetch profiles logic here
      // For example: fetch profiles from GoLogin API
      window.alert('Tính năng Fetch Profiles đang được phát triển!');
    } catch (error) {
      console.error('Error fetching profiles:', error);
      window.alert(`Lỗi khi fetch profiles: ${error.message}`);
    }
  };

  // Handle download single profile
  const handleDownloadProfile = async (profileId) => {
    console.log('Download profile clicked:', profileId);
    try {
      // Implement download single profile logic here
      // For example: download specific profile from GoLogin API
      window.alert(`Tính năng Download Profile ${profileId} đang được phát triển!`);
    } catch (error) {
      console.error('Error downloading profile:', error);
      window.alert(`Lỗi khi download profile: ${error.message}`);
    }
  };

  // Load profiles function (separated for reuse)
  const loadProfiles = async () => {
    console.log('🔄 Starting to load profiles...');
    try {
      const result = await window.electronAPI.getLocalProfiles();
      console.log('📥 Got result from electronAPI:', result);
      
      if (result.success) {
        setProfiles(result.profiles);
        setCurrentDirectory(result.directory);
        setNeedSelectDirectory(result.needSelectDirectory || false);
        
        console.log('✅ Loaded local profiles:', result.profiles);
        console.log('📊 Number of profiles loaded:', result.profiles.length);
        console.log('📁 Directory:', result.directory);
      } else {
        console.error('❌ Error loading local profiles:', result.error);
      }
    } catch (error) {
      console.error('💥 Exception loading profiles:', error.message);
    }
  };

  // Handle directory change
  const handleDirectoryChange = (newDirectory) => {
    console.log('📁 Directory changed to:', newDirectory);
    setCurrentDirectory(newDirectory);
    setNeedSelectDirectory(false);
    // Reload profiles from new directory
    loadProfiles();
  };

  // Load profiles on component mount
  useEffect(() => {
    loadProfiles();
  }, []);

  // Expose reload for children (Manage tab)
  const handleReloadLocalProfiles = async () => {
    await loadProfiles();
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <Header
        onAddProfile={handleAddProfile}
        onBuyProxy={handleBuyProxy}
        onMenuClick={handleMenuClick}
        onSettingsClick={handleSettingsClick}
        onSearch={handleSearch}
      />
      
      <main className="px-6 py-6">
        {/* Directory Selector */}
        <DirectorySelector onDirectoryChange={handleDirectoryChange} />
        
        
        {/* Search Results Info */}
        {searchQuery && (
          <div className="mb-4 text-sm text-gray-600">
            Showing {filteredProfiles.length} of {profiles.length} profiles
            {searchQuery && ` for "${searchQuery}"`}
          </div>
        )}
        
       
        
        <TabContainer
          profiles={filteredProfiles}
          onLaunchProfile={handleLaunchProfile}
          onViewProfile={handleViewProfile}
          onStopProfile={handleStopProfile}
          onProfileSelect={handleProfileSelect}
          onEditProfile={handleEditProfile}
          onDeleteProfile={handleDeleteProfile}
          onDownloadAll={handleDownloadAll}
          onReloadLocal={handleReloadLocalProfiles}
          onDownloadProfile={handleDownloadProfile}
        />
      </main>
    </div>
  );
}

export default App;
