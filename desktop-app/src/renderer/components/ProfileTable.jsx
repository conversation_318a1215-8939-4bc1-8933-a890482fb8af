import React, { useState } from 'react';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Checkbox } from "@/components/ui/checkbox";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";
import { Separator } from "@/components/ui/separator";
import { 
  Play, 
  Edit, 
  Trash2, 
  Plus, 
  MoreHorizontal, 
  ArrowUpDown, 
  Settings,
  FolderOpen,
  Circle,
  Download,
  Eye,
  Square
} from "lucide-react";
import { useSettings } from "../store/settingsStore";

const ProfileTable = ({ 
  profiles, 
  onLaunchProfile, 
  onViewProfile,
  onProfileSelect, 
  onEditProfile, 
  onDeleteProfile,
  showDownloadButtons = false,
  onDownloadProfile,
  showRunButton = true,
  showCheckbox = true,
  onStopProfile
}) => {
  const [sortField, setSortField] = useState('name');
  const [sortDirection, setSortDirection] = useState('asc');
  const [selectedProfiles, setSelectedProfiles] = useState(new Set());
  const [runningProfiles, setRunningProfiles] = useState(new Set());
  const { apiToken, folderDirectory } = useSettings();

  const handleAddQuickProfile = async () => {
    if (!apiToken) {
      return window.alert('Chưa có API Token. Vui lòng lưu token ở phần Thư mục Profiles.');
    }
    if (!folderDirectory) {
      return window.alert('Chưa chọn thư mục Profiles. Vui lòng chọn trong Thư mục Profiles.');
    }
    try {
      const result = await window.electronAPI.createDownloadOpenProfile({ 
        token: apiToken, 
        folderDirectory 
      });
      if (result.success) {
        console.log('✅ createDownloadAndOpenProfile success:', result);
        window.alert(`Profile ${result.profileId} đã được tạo và mở thành công!`);
      } else {
        throw new Error(result.error || 'Yêu cầu thất bại');
      }
    } catch (err) {
      console.error('❌ createDownloadAndOpenProfile error:', err);
      window.alert(`Lỗi: ${err.message || err}`);
    }
  };

  const handleSort = (field) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('asc');
    }
  };

  const handleProfileSelect = (profileId) => {
    const newSelected = new Set(selectedProfiles);
    if (newSelected.has(profileId)) {
      newSelected.delete(profileId);
    } else {
      newSelected.add(profileId);
    }
    setSelectedProfiles(newSelected);
    onProfileSelect(profileId);
  };

  const handleSelectAll = () => {
    if (selectedProfiles.size === profiles.length) {
      setSelectedProfiles(new Set());
    } else {
      setSelectedProfiles(new Set(profiles.map(p => p.id)));
    }
  };

  const handleLaunchProfile = (profileId) => {
    setRunningProfiles(prev => new Set([...prev, profileId]));
    onLaunchProfile(profileId);
  };

  const handleStopProfile = (profileId) => {
    setRunningProfiles(prev => {
      const newSet = new Set(prev);
      newSet.delete(profileId);
      return newSet;
    });
    if (onStopProfile) {
      onStopProfile(profileId);
    }
  };

  const sortedProfiles = [...profiles].sort((a, b) => {
    const aValue = a[sortField] || '';
    const bValue = b[sortField] || '';
    
    if (sortDirection === 'asc') {
      return aValue.localeCompare(bValue);
    } else {
      return bValue.localeCompare(aValue);
    }
  });

  return (
    <Card>
      {/* Table Header with Bulk Actions */}
      {profiles.length > 0 && showCheckbox && (
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <Checkbox
                checked={selectedProfiles.size === profiles.length}
                onCheckedChange={handleSelectAll}
              />
              <span className="text-sm text-muted-foreground">
                {selectedProfiles.size} of {profiles.length} selected
              </span>
            </div>
            
            {selectedProfiles.size > 0 && (
              <div className="flex items-center gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    selectedProfiles.forEach(id => onEditProfile(id));
                  }}
                >
                  <Edit className="mr-2 h-4 w-4" />
                  Edit Selected
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    if (window.confirm(`Are you sure you want to delete ${selectedProfiles.size} profile(s)?`)) {
                      selectedProfiles.forEach(id => onDeleteProfile(id));
                      setSelectedProfiles(new Set());
                    }
                  }}
                  className="text-red-600 border-red-300 hover:bg-red-50"
                >
                  <Trash2 className="mr-2 h-4 w-4" />
                  Delete Selected
                </Button>
              </div>
            )}
          </div>
        </CardHeader>
      )}

      <CardContent className="p-0">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className="w-12">
                <div className="flex items-center gap-2">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handleSort('name')}
                    className="h-auto p-0 font-medium hover:bg-transparent"
                  >
                    Name
                    <ArrowUpDown className="ml-2 h-4 w-4" />
                  </Button>
                  <Button 
                    variant="ghost"
                    size="sm"
                    onClick={handleAddQuickProfile}
                    className="h-6 w-6 p-0 text-green-600 hover:text-green-700 hover:bg-green-50"
                    title="Add new profile"
                  >
                    <Plus className="h-4 w-4" />
                  </Button>
                </div>
              </TableHead>
              <TableHead>State</TableHead>
              <TableHead>Notes</TableHead>
              <TableHead>Location</TableHead>
              <TableHead className="w-12">
                <Button variant="ghost" size="sm" className="h-6 w-6 p-0">
                  <Settings className="h-4 w-4" />
                </Button>
              </TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {sortedProfiles.length === 0 ? (
              <TableRow>
                <TableCell colSpan={5} className="h-32 text-center">
                  <div className="flex flex-col items-center gap-3">
                    <FolderOpen className="h-12 w-12 text-muted-foreground" />
                    <div className="space-y-1">
                      <p className="text-lg font-medium">No profiles found</p>
                      <p className="text-sm text-muted-foreground">Create your first profile to get started</p>
                    </div>
                    <Button onClick={() => onEditProfile('new')}>
                      <Plus className="mr-2 h-4 w-4" />
                      Create Profile
                    </Button>
                  </div>
                </TableCell>
              </TableRow>
            ) : (
              sortedProfiles.map((profile) => (
                <TableRow key={profile.id} className="hover:bg-muted/50">
                  <TableCell>
                    <div className="flex items-center gap-3">
                      {showCheckbox && (
                        <Checkbox
                          checked={selectedProfiles.has(profile.id)}
                          onCheckedChange={() => handleProfileSelect(profile.id)}
                        />
                      )}
                      <div className="flex items-center gap-2">
                        <span className="font-medium">{profile.name}</span>
                        <div className={`w-2 h-2 rounded-full ${
                          profile.status === 'active' ? 'bg-green-500' : 
                          profile.status === 'inactive' ? 'bg-muted-foreground' : 'bg-yellow-500'
                        }`}></div>
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-3">
                      {showRunButton && (
                        <>
                          {runningProfiles.has(profile.id) ? (
                            <div className="flex items-center gap-2">
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => onViewProfile && onViewProfile(profile.id)}
                                className="text-blue-600 border-blue-300 hover:bg-blue-50"
                              >
                                <Eye className="mr-2 h-4 w-4" />
                                View
                              </Button>
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => handleStopProfile(profile.id)}
                                className="text-red-600 border-red-300 hover:bg-red-50"
                              >
                                <Square className="mr-2 h-4 w-4" />
                                Stop
                              </Button>
                            </div>
                          ) : (
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => handleLaunchProfile(profile.id)}
                              className="text-green-600 border-green-300 hover:bg-green-50"
                            >
                              <Play className="mr-2 h-4 w-4" />
                              Run
                            </Button>
                          )}
                        </>
                      )}
                      <div className="flex items-center gap-2">
                        <Circle className={`h-4 w-4 ${
                          runningProfiles.has(profile.id) ? 'text-green-500' : 'text-muted-foreground'
                        }`} />
                        <span className="text-sm text-muted-foreground">
                          {runningProfiles.has(profile.id) ? 'running' : 'ready'}
                        </span>
                      </div>
                    </div>
                  </TableCell>
                  <TableCell className="text-muted-foreground">
                    {profile.notes || '-'}
                  </TableCell>
                  <TableCell className="text-muted-foreground">
                    {profile.location || '-'}
                  </TableCell>
                  <TableCell>
                    {showDownloadButtons ? (
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => onDownloadProfile && onDownloadProfile(profile.id)}
                        className="text-blue-600 border-blue-300 hover:bg-blue-50"
                      >
                        <Download className="mr-2 h-4 w-4" />
                        Download
                      </Button>
                    ) : null}
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </CardContent>
    </Card>
  );
};

export default ProfileTable;
