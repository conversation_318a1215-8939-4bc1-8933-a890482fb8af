import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>ger, <PERSON><PERSON>Content } from "@/components/ui/tabs";
import { Button } from "@/components/ui/button";
import { Download, RefreshCw } from "lucide-react";
import ProfileTable from './ProfileTable';
import { useSettings } from "../store/settingsStore";

const TabContainer = ({ 
  profiles, 
  onLaunchProfile, 
  onViewProfile,
  onStopProfile,
  onProfileSelect, 
  onEditProfile, 
  onDeleteProfile,
  onDownloadAll,
  onDownloadProfile,
  onReloadLocal
}) => {
  const [activeTab, setActiveTab] = useState('manage');
  const [serverProfiles, setServerProfiles] = useState([]);
  const [isFetching, setIsFetching] = useState(false);
  const { apiToken } = useSettings();
  const { folderDirectory } = useSettings();

  const handleDownloadAll = async () => {
    if (onDownloadAll) {
      await onDownloadAll();
    }
  };

  const handleFetchProfiles = async () => {
    if (!apiToken) {
      window.alert('Chưa có API Token. Vui lòng lưu token ở phần Thư mục Profiles.');
      return;
    }
    try {
      setIsFetching(true);
      // Always fetch directly from renderer (works in dev and Electron)
      const url = 'https://api.gologin.com/browser/v2';
      const resp = await fetch(url, {
        method: 'GET',
        headers: { Authorization: `Bearer ${apiToken}` },
      });
      if (!resp.ok) {
        const text = await resp.text();
        throw new Error(text || `HTTP ${resp.status}`);
      }
      const data = await resp.json();

      // Support various shapes; your docs show { profiles: [...] }
      const list = Array.isArray(data?.profiles)
        ? data.profiles
        : Array.isArray(data?.data)
          ? data.data
          : Array.isArray(data?.browsers)
            ? data.browsers
            : Array.isArray(data)
              ? data
              : [];
      console.log('[Get Profile] items:', list.length);
      const mapped = list.map((p) => {
        const proxy = p.proxy || {};
        const location = typeof proxy === 'string'
          ? proxy
          : (proxy.host && proxy.port) ? `${proxy.host}:${proxy.port}`
            : proxy.type || p.proxyType || '-';
        return {
          id: p.id || p._id || '',
          name: p.name || 'Unnamed',
          notes: p.notes || '',
          location,
          status: p.status || 'ready',
        };
      });
      setServerProfiles(mapped);
    } catch (error) {
      console.error(error);
      window.alert(`Lỗi tải danh sách profile: ${error.message}`);
    } finally {
      setIsFetching(false);
    }
  };

  return (
    <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
      <div className="flex justify-start">
        <TabsList>
          <TabsTrigger value="manage">Manage Profile</TabsTrigger>
          <TabsTrigger value="download">Download Profile</TabsTrigger>
        </TabsList>
      </div>
      
      <TabsContent value="manage" className="mt-0">
        <div className="space-y-1">
          {/* Manage Profile Actions */}
          <div className="flex items-center justify-between p-4">
            <div className="flex items-center gap-2">
              <h3 className="text-md font-semibold">Manage Profile Actions</h3>
            </div>
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={async () => { if (onReloadLocal) await onReloadLocal(); }}
              >
                <RefreshCw className="h-4 w-4 mr-2" />
                Reload
              </Button>
            </div>
          </div>
          
          {/* Profile Table */}
          <ProfileTable
            profiles={profiles}
            onLaunchProfile={onLaunchProfile}
            onViewProfile={onViewProfile}
            onStopProfile={onStopProfile}
            onProfileSelect={onProfileSelect}
            onEditProfile={onEditProfile}
            onDeleteProfile={onDeleteProfile}
            showDownloadButtons={false}
          />
        </div>
      </TabsContent>
      
      <TabsContent value="download" className="mt-0">
        <div className="space-y-1">
          {/* Download Profile Actions */}
          <div className="flex items-center justify-between p-4">
            <div className="flex items-center gap-2">
              <h3 className="text-md font-semibold">Download Profile Actions</h3>
            </div>
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={handleFetchProfiles}
              >
                <RefreshCw className="h-4 w-4 mr-2" />
                {isFetching ? 'Loading...' : 'Get Profile'}
              </Button>
              <Button
                size="sm"
                onClick={handleDownloadAll}
              >
                <Download className="h-4 w-4 mr-2" />
                Download All
              </Button>
            </div>
          </div>
          
          {/* Profile Table with Download Buttons */}
          <ProfileTable
            profiles={serverProfiles}
            onLaunchProfile={onLaunchProfile}
            onProfileSelect={onProfileSelect}
            onEditProfile={onEditProfile}
            onDeleteProfile={onDeleteProfile}
            showDownloadButtons={true}
            onDownloadProfile={async (profileId) => {
              if (!apiToken) return window.alert('Chưa có API Token.');
              if (!folderDirectory) return window.alert('Please select a folder to save the profile');
              const res = await window.electronAPI.downloadProfile({ token: apiToken, profileId, folderDirectory });
              if (res?.success) {
                window.alert('Downloaded to: ' + res.profilePath);
                // Auto-reload profiles list
                if (onReloadLocal) {
                  await onReloadLocal();
                }
              } else {
                window.alert('Download failed: ' + (res?.error || 'unknown error'));
              }
            }}
            showRunButton={false}
            showCheckbox={false}
          />
        </div>
      </TabsContent>
    </Tabs>
  );
};

export default TabContainer;
