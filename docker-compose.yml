version: '3.8'

services:
  gologin-browser:
    image: node:18-bullseye
    platform: linux/amd64
    container_name: gologin-browser
    working_dir: /app
    volumes:
      - .:/app
      - node_modules_cache:/app/node_modules
    environment:
      - GL_API_TOKEN=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiI2OGFjNjI2ZTMwZjc3ZDljMjAxYzVmN2YiLCJ0eXBlIjoiZGV2Iiwiand0aWQiOiI2OGIzNTYwNTcyODdhNzZiZWE3YzliMzUifQ.RT4XYoH2kBpPIHZoYMfIbquqHzZOpT5KHZXyXB2FN-E
      - NODE_ENV=development
    ports:
      - "9222:9222"
    command: >
      sh -c "
        apt-get update &&
        apt-get install -y libnss3 libatk-bridge2.0-0 libdrm2 libxkbcommon0 libgbm1 libxss1 libasound2 fonts-liberation libcups2 libxcomposite1 libxdamage1 libxrandr2 libxfixes3 libxtst6 libxi6 libx11-xcb1 libx11-6 libxcb1 libxext6 libxrender1 libcairo2 libglib2.0-0 libgtk-3-0 libgdk-pixbuf2.0-0 libpango-1.0-0 libpangocairo-1.0-0 libatk1.0-0 libcairo-gobject2 libgconf-2-4 libxshmfence1 libgl1-mesa-glx libgl1-mesa-dri libegl1-mesa libxau6 libxdmcp6 libappindicator3-1 xdg-utils &&
        npm install &&
        node examples/puppeter/local-browser.js
      "
    networks:
      - gologin-network

volumes:
  node_modules_cache:

networks:
  gologin-network:
    driver: bridge 