import puppeteer from 'puppeteer-core';
import path from 'path';
import fs from 'fs';
import { GologinApi } from '../gologin-api.js';
import GoLogin from '../gologin.js';

/**
 * Mở browser từ profile folder dưới local sử dụng GoLogin API
 * @param {Object} options - <PERSON><PERSON><PERSON> chọn cấu hình
 * @param {string} options.profileId - Profile ID cần mở
 * @param {string} options.profilesDir - Thư mục chứa profiles (mặc định: './gologin_profiles')
 * @param {string} options.token - GoLogin API token
 * @param {string} options.executablePath - Đường dẫn tới Chrome executable (tùy chọn)
 * @returns {Promise<Object>} Browser instance và thông tin profile
 */
const launchBrowserFromLocal = async (options = {}) => {
  const {
    profileId,
    profilesDir = './gologin_profiles',
    token,
    executablePath,
  } = options;
  
  // Kiểm tra profile ID
  if (!profileId) {
    throw new Error('Profile ID is required');
  }

  // Kiểm tra token
  if (!token) {
    throw new Error('GoLogin API token is required');
  }

  // Kiểm tra thư mục profiles tồn tại
  if (!fs.existsSync(profilesDir)) {
    throw new Error(`Profiles directory not found: ${profilesDir}`);
  }

  // Kiểm tra profile folder tồn tại
  const profileFolder = path.join(profilesDir, `gologin_profile_${profileId}`);
  if (!fs.existsSync(profileFolder)) {
    throw new Error(`Profile folder not found: ${profileFolder}`);
  }

  console.log('🔍 Options received:', JSON.stringify(options, null, 2));
  console.log('🔍 executablePath value:', options.executablePath);
  console.log('🔍 executablePath type:', typeof options.executablePath);

  try {
    // Sử dụng GoLogin API để launch profile từ local
    const api = GologinApi({ token });
    
    const { browser, gl } = await api.launch({
      profileId,
      local: true,
      localStorageDir: profilesDir
      // GoLogin sẽ tự động detect và sử dụng browser version phù hợp
    });

    // Lưu GoLogin instance để có thể stop sau này
    if (gl) {
      activeGologinInstances.set(profileId, gl);
      console.log(`📝 Stored GoLogin instance for profile: ${profileId}`);
    }

    // Lưu Browser instance để hỗ trợ View
    if (browser) {
      __gologinGlobal.activeBrowsers.set(profileId, browser);
      console.log(`📝 Stored Browser instance for profile: ${profileId}`);
    }

    return {
      browser,
      profileId,
      profileFolder,
      success: true,
      api // Trả về api để có thể cleanup sau này
    };

  } catch (error) {
    console.error('🔍 Error details:', {
      name: error.name,
      message: error.message,
      stack: error.stack?.split('\n').slice(0, 5).join('\n')
    });
    throw new Error(`Failed to launch browser: ${error.message}`);
  }
};

/**
 * Lấy danh sách profiles từ folder local
 * @param {string} profilesDir - Thư mục chứa profiles
 * @returns {Array} Danh sách profiles
 */
const getLocalProfiles = (profilesDir = './gologin_profiles') => {
  if (!fs.existsSync(profilesDir)) {
    console.warn(`Profiles directory not found: ${profilesDir}`);
    return [];
  }

  const entries = fs.readdirSync(profilesDir, { withFileTypes: true });
  const profiles = entries
    .filter((e) => e.isDirectory())
    .filter((e) => e.name.startsWith('gologin_profile_'))
    .map((e) => {
      const rawName = e.name;
      const id = rawName.startsWith('gologin_profile_') ? rawName.replace('gologin_profile_', '') : rawName;
      return {
        id,
        name: rawName,
        path: path.join(profilesDir, rawName),
        status: 'ready',
      };
    });

  return profiles;
}
/**
 * Đóng browser và cleanup GoLogin API
 * @param {Object} browser - Browser instance
 * @param {Object} api - GoLogin API instance (tùy chọn)
 */
const closeBrowser = async (browser, api = null) => {
  try {
    if (browser) {
      await browser.close();
      console.log('✅ Browser closed');
    }
    
    if (api) {
      await api.exit();
      console.log('✅ GoLogin API cleaned up');
    }
  } catch (error) {
    console.error('❌ Error closing browser:', error.message);
  }
};

// Global registry to persist instances across dynamic imports
const __gologinGlobal = (globalThis.__gologinGlobal = globalThis.__gologinGlobal || {
  activeGologinInstances: new Map(),
  activeBrowsers: new Map(),
});

const activeGologinInstances = __gologinGlobal.activeGologinInstances;
const activeBrowsers = __gologinGlobal.activeBrowsers;

/**
 * Dừng browser profile đang chạy dưới local bằng cách tái sử dụng GoLogin methods
 * @param {Object} options - Tùy chọn cấu hình
 * @param {string} options.profileId - Profile ID cần dừng
 * @param {string} options.token - GoLogin API token
 * @param {string} options.profilesDir - Thư mục chứa profiles (mặc định: './gologin_profiles')
 * @param {boolean} options.killBrowser - Kill browser process (mặc định: true)
 * @param {boolean} options.saveProfile - Lưu profile lên server (mặc định: false)
 * @returns {Promise<Object>} Kết quả dừng profile
 */
const stopLocalProfile = async (options = {}) => {
  const {
    profileId,
    token,
    profilesDir = './gologin_profiles',
    killBrowser = true,
    saveProfile = false
  } = options;

  if (!profileId) {
    throw new Error('Profile ID is required');
  }

  if (!token) {
    throw new Error('GoLogin API token is required');
  }

  console.log(`🛑 Stopping local profile: ${profileId}`);

  try {
    let result = {
      success: false,
      profileId,
      killed: false,
      saved: false,
      message: ''
    };

    // Kiểm tra xem có GoLogin instance đang chạy không
    const activeInstance = activeGologinInstances.get(profileId);
    
    if (activeInstance) {
      console.log(`🎯 Found active GoLogin instance for profile: ${profileId}`);
      
      if (killBrowser) {
        try {
          activeInstance.killBrowser();
          result.killed = true;
          console.log(`✅ Browser process killed for profile: ${profileId}`);
        } catch (error) {
          console.warn(`⚠️ Could not kill browser directly:`, error.message);
          
          // Fallback: Sử dụng stopBrowser() cho Linux/macOS
          try {
            await activeInstance.stopBrowser();
            result.killed = true;
            console.log(`✅ Browser stopped via port for profile: ${profileId}`);
          } catch (portError) {
            console.warn(`⚠️ Could not stop browser via port:`, portError.message);
          }
        }
      }

      if (saveProfile) {
        try {
          await activeInstance.stopLocal({ posting: true });
          result.saved = true;
          console.log(`✅ Profile saved for: ${profileId}`);
        } catch (error) {
          console.warn(`⚠️ Could not save profile:`, error.message);
        }
      }

      // Xóa instance khỏi active list
      activeGologinInstances.delete(profileId);
      __gologinGlobal.activeBrowsers.delete(profileId);
      
    } else {
      console.log(`⚠️ No active GoLogin instance found for profile: ${profileId}`);
      console.log(`🔄 Trying to find and kill browser process by profile path...`);
      
      // Fallback: Tìm và kill process bằng profile path
      const { execSync } = await import('child_process');
      const os = await import('os');
      const platform = os.platform();
      const profilePath = path.join(profilesDir, `gologin_profile_${profileId}`);
      
      if (fs.existsSync(profilePath)) {
        let killed = false;
        
        if (platform === 'win32') {
          try {
            const cmd = `wmic process where "CommandLine like '%${profilePath.replace(/\\/g, '\\\\')}%'" get ProcessId /format:csv`;
            const output = execSync(cmd, { encoding: 'utf8' });
            
            const lines = output.split('\n').filter(line => line.trim() && !line.startsWith('Node,'));
            
            for (const line of lines) {
              const parts = line.split(',');
              if (parts.length >= 3) {
                const pid = parts[2]?.trim();
                if (pid && !isNaN(pid)) {
                  try {
                    execSync(`taskkill /F /PID ${pid}`, { encoding: 'utf8' });
                    console.log(`✅ Killed process PID: ${pid}`);
                    killed = true;
                  } catch (killError) {
                    console.warn(`⚠️ Could not kill process ${pid}:`, killError.message);
                  }
                }
              }
            }
          } catch (error) {
            console.warn('⚠️ Error finding processes on Windows:', error.message);
          }
        } else {
          try {
            const pgrepCmd = `pgrep -f "${profilePath}"`;
            const pids = execSync(pgrepCmd, { encoding: 'utf8' }).trim().split('\n').filter(pid => pid);
            
            for (const pid of pids) {
              try {
                process.kill(parseInt(pid), 'SIGTERM');
                console.log(`✅ Killed process PID: ${pid}`);
                killed = true;
              } catch (killError) {
                console.warn(`⚠️ Could not kill process ${pid}:`, killError.message);
              }
            }
          } catch (error) {
            console.warn('⚠️ Error finding processes on Unix:', error.message);
          }
        }
        
        result.killed = killed;
        if (killed) {
          __gologinGlobal.activeBrowsers.delete(profileId);
        }
      }
    }

    result.success = result.killed || result.saved;
    result.message = result.success 
      ? `Profile ${profileId} stopped successfully (killed: ${result.killed}, saved: ${result.saved})`
      : `Could not stop profile ${profileId}`;

    return result;

  } catch (error) {
    console.error(`❌ Error stopping local profile ${profileId}:`, error.message);
    throw new Error(`Failed to stop local profile ${profileId}: ${error.message}`);
  }
};

/**
 * Bring-to-front tất cả tab thuộc browser của profile
 * @param {Object} options
 * @param {string} options.profileId
 * @returns {Promise<{success:boolean, pages:number}>}
 */
const viewLocalProfile = async ({ profileId }) => {
  if (!profileId) {
    throw new Error('Profile ID is required');
  }
  const browser = activeBrowsers.get(profileId);
  if (!browser) {
    return { success: false, pages: 0, message: 'No browser instance for this profile' };
  }
  try {
    const pages = await browser.pages();
    let count = 0;
    for (const page of pages) {
      try {
        await page.bringToFront();
        count++;
      } catch {}
    }

    // Bring native window to front on Windows
    try {
      const os = await import('os');
      if (os.platform() === 'win32') {
        const gl = activeGologinInstances.get(profileId);
        const pid = gl?.processSpawned?.pid;
        if (pid) {
          const { execFileSync } = await import('child_process');
          const ps = `Add-Type @"
using System;
using System.Runtime.InteropServices;
public class WinAPI {
  [DllImport("user32.dll")] public static extern bool SetForegroundWindow(IntPtr hWnd);
  [DllImport("user32.dll")] public static extern bool ShowWindow(IntPtr hWnd, int nCmdShow);
}
"@;$p = Get-Process -Id ${pid} -ErrorAction SilentlyContinue; if ($p -and $p.MainWindowHandle -ne 0) { [WinAPI]::ShowWindow($p.MainWindowHandle, 9) | Out-Null; [WinAPI]::SetForegroundWindow($p.MainWindowHandle) | Out-Null }`;
          execFileSync('powershell', ['-NoProfile', '-WindowStyle', 'Hidden', '-Command', ps]);
        }
      }
    } catch {}

    return { success: count > 0, pages: count };
  } catch (e) {
    return { success: false, pages: 0, message: e.message };
  }
};

/**
 * Tạo profile mới và mở browser nhanh với tên mặc định "Profile"
 * @param {Object} options - Tùy chọn cấu hình
 * @param {string} options.token - GoLogin API token
 * @param {string} options.folderDirectory - Thư mục để lưu profile
 * @returns {Promise<Object>} Thông tin profile đã tạo
 */
async function createDownloadAndOpenProfile({ token, folderDirectory }) {
  try {
    console.log('📋 Parameters:', { token: !!token, folderDirectory });
    
    if (!token) throw new Error('token is required');
    if (!folderDirectory) throw new Error('folderDirectory is required');

    const gologin = GologinApi({ 
      token,
      localStorageDir: folderDirectory,
    });

    // Tạo nhanh: sử dụng tên mặc định "Profile"
    const { browser, profileId, gl } = await gologin.launch({
      localStorageDir: folderDirectory
      // GoLogin sẽ tự động detect và sử dụng browser version phù hợp
    });

    // Lưu GoLogin instance để có thể stop sau này
    if (gl) {
      activeGologinInstances.set(profileId, gl);
      console.log(`📝 Stored GoLogin instance for profile: ${profileId}`);
    }
    console.log('✅ Profile launched successfully:', profileId);

    const page = await browser.newPage();
    await page.goto('https://iphey.com/', { waitUntil: 'networkidle2' });
    
    const createdProfilePath = path.join(folderDirectory, `gologin_profile_${profileId}`);
    console.log(`📂 Đường dẫn profile: ${createdProfilePath}`);

   
    return {
      success: true,
      profileId,
      profileName: displayName,
      profilePath: createdProfilePath
    };
    
  } catch (error) {
    console.error(`❌ Lỗi tạo profile:`, error.message);
    throw error;
  }
}

/**
 * Download profile data from GoLogin server to local folder (without launching browser)
 * @param {Object} params
 * @param {string} params.token - GoLogin API token
 * @param {string} params.profileId - Profile ID on server
 * @param {string} params.folderDirectory - Destination base directory for profiles
 * @returns {Promise<{success:boolean, profilePath:string}>}
 */
async function downloadProfileFromServer({ token, profileId, folderDirectory }) {
  console.log('📋 Download parameters:', { token: !!token, profileId, folderDirectory });
  
  if (!token) throw new Error('token is required');
  if (!profileId) throw new Error('profileId is required');
  if (!folderDirectory) throw new Error('folderDirectory is required');
  if (!fs.existsSync(folderDirectory)) {
    throw new Error(`Please select a folder to save the profile`);
  }

  // Kiểm tra profile đã tồn tại chưa
  const profilePath = path.join(folderDirectory, `gologin_profile_${profileId}`);
  const profileExists = fs.existsSync(profilePath);
  
  console.log('🔍 Profile exists locally:', profileExists);
  console.log('📂 Profile path:', profilePath);

  // Sử dụng GologinApi như các function khác
  const api = GologinApi({ token });
  
  if (profileExists) {
    // Profile đã tồn tại, thông báo và không làm gì
    console.log('⚠️ Profile already exists locally:', profilePath);
    throw new Error('Profile đã được tải xuống rồi');
  } else {
    // Profile chưa tồn tại, download từ server
    console.log('⬇️ Downloading profile from server...');
    const { browser } = await api.launch({
      profileId,
      localStorageDir: folderDirectory
      // Không truyền local: true vì muốn download từ server
    });
    console.log('✅ Profile downloaded and launched:', profilePath);
    return { success: true, profilePath, browser, fromLocal: false };
  }
}

// Export cho ES modules
export {
  launchBrowserFromLocal,
  getLocalProfiles,
  closeBrowser,
  stopLocalProfile,
  viewLocalProfile,
  createDownloadAndOpenProfile,
  downloadProfileFromServer
};
