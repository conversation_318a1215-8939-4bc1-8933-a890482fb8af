import puppeteer from 'puppeteer-core';
import path from 'path';
import fs from 'fs';
import { GologinApi } from '../gologin-api.js';
import GoLogin from '../gologin.js';

/**
 * Mở browser từ profile folder dưới local sử dụng GoLogin API
 * @param {Object} options - <PERSON><PERSON><PERSON> chọn cấu hình
 * @param {string} options.profileId - Profile ID cần mở
 * @param {string} options.profilesDir - Thư mục chứa profiles (mặc định: './gologin_profiles')
 * @param {string} options.token - GoLogin API token
 * @param {string} options.executablePath - Đường dẫn tới Chrome executable (tùy chọn)
 * @returns {Promise<Object>} Browser instance và thông tin profile
 */
const launchBrowserFromLocal = async (options = {}) => {
  const {
    profileId,
    profilesDir = './gologin_profiles',
    token,
    executablePath,
  } = options;

  // Kiểm tra profile ID
  if (!profileId) {
    throw new Error('Profile ID is required');
  }

  // Kiểm tra token
  if (!token) {
    throw new Error('GoLogin API token is required');
  }

  // Kiểm tra thư mục profiles tồn tại
  if (!fs.existsSync(profilesDir)) {
    throw new Error(`Profiles directory not found: ${profilesDir}`);
  }

  // Kiểm tra profile folder tồn tại
  const profileFolder = path.join(profilesDir, `gologin_profile_${profileId}`);
  if (!fs.existsSync(profileFolder)) {
    throw new Error(`Profile folder not found: ${profileFolder}`);
  }

  console.log('🔍 Options received:', JSON.stringify(options, null, 2));
  console.log('🔍 executablePath value:', options.executablePath);
  console.log('🔍 executablePath type:', typeof options.executablePath);

  try {
    // Sử dụng GoLogin API để launch profile từ local
    const api = GologinApi({ token });

    const { browser } = await api.launch({
      profileId,
      local: true,
      localStorageDir: profilesDir
      // Không truyền executablePath để dùng Orbit mặc định
    });

    return {
      browser,
      profileId,
      profileFolder,
      success: true,
      api // Trả về api để có thể cleanup sau này
    };

  } catch (error) {
    console.error('🔍 Error details:', {
      name: error.name,
      message: error.message,
      stack: error.stack?.split('\n').slice(0, 5).join('\n')
    });
    throw new Error(`Failed to launch browser: ${error.message}`);
  }
};

/**
 * Lấy danh sách profiles từ folder local
 * @param {string} profilesDir - Thư mục chứa profiles
 * @returns {Array} Danh sách profiles
 */
const getLocalProfiles = (profilesDir = './gologin_profiles') => {
  if (!fs.existsSync(profilesDir)) {
    console.warn(`Profiles directory not found: ${profilesDir}`);
    return [];
  }

  const entries = fs.readdirSync(profilesDir, { withFileTypes: true });
  const profiles = entries
    .filter((e) => e.isDirectory())
    .filter((e) => e.name.startsWith('gologin_profile_'))
    .map((e) => {
      const rawName = e.name;
      const id = rawName.startsWith('gologin_profile_') ? rawName.replace('gologin_profile_', '') : rawName;
      return {
        id,
        name: rawName,
        path: path.join(profilesDir, rawName),
        status: 'ready',
      };
    });

  return profiles;
}
/**
 * Đóng browser và cleanup GoLogin API
 * @param {Object} browser - Browser instance
 * @param {Object} api - GoLogin API instance (tùy chọn)
 */
const closeBrowser = async (browser, api = null) => {
  try {
    if (browser) {
      await browser.close();
      console.log('✅ Browser closed');
    }

    if (api) {
      await api.exit();
      console.log('✅ GoLogin API cleaned up');
    }
  } catch (error) {
    console.error('❌ Error closing browser:', error.message);
  }
};

/**
 * Tạo profile mới và mở browser nhanh với tên mặc định "Profile"
 * @param {Object} options - Tùy chọn cấu hình
 * @param {string} options.token - GoLogin API token
 * @param {string} options.folderDirectory - Thư mục để lưu profile
 * @returns {Promise<Object>} Thông tin profile đã tạo
 */
async function createDownloadAndOpenProfile({ token, folderDirectory }) {
  try {
    console.log('📋 Parameters:', { token: !!token, folderDirectory });

    if (!token) throw new Error('token is required');
    if (!folderDirectory) throw new Error('folderDirectory is required');

    const gologin = GologinApi({
      token,
      localStorageDir: folderDirectory,
    });

    // Tạo nhanh: sử dụng tên mặc định "Profile"
    const { browser, profileId } = await gologin.launch({
      localStorageDir: folderDirectory,
    });
    console.log('✅ Profile launched successfully:', profileId);

    const page = await browser.newPage();
    await page.goto('https://iphey.com/', { waitUntil: 'networkidle2' });

    const createdProfilePath = path.join(folderDirectory, `gologin_profile_${profileId}`);
    console.log(`📂 Đường dẫn profile: ${createdProfilePath}`);


    return {
      success: true,
      profileId,
      profileName: 'Profile',
      profilePath: createdProfilePath
    };

  } catch (error) {
    console.error(`❌ Lỗi tạo profile:`, error.message);
    throw error;
  }
}

/**
 * Download profile data from GoLogin server to local folder (without launching browser)
 * @param {Object} params
 * @param {string} params.token - GoLogin API token
 * @param {string} params.profileId - Profile ID on server
 * @param {string} params.folderDirectory - Destination base directory for profiles
 * @returns {Promise<{success:boolean, profilePath:string}>}
 */
async function downloadProfileFromServer({ token, profileId, folderDirectory }) {
  console.log('📋 Download parameters:', { token: !!token, profileId, folderDirectory });

  if (!token) throw new Error('token is required');
  if (!profileId) throw new Error('profileId is required');
  if (!folderDirectory) throw new Error('folderDirectory is required');
  if (!fs.existsSync(folderDirectory)) {
    throw new Error(`Please select a folder to save the profile`);
  }

  // Kiểm tra profile đã tồn tại chưa
  const profilePath = path.join(folderDirectory, `gologin_profile_${profileId}`);
  const profileExists = fs.existsSync(profilePath);

  console.log('🔍 Profile exists locally:', profileExists);
  console.log('📂 Profile path:', profilePath);

  // Sử dụng GologinApi như các function khác
  const api = GologinApi({ token });

  if (profileExists) {
    // Profile đã tồn tại, thông báo và không làm gì
    console.log('⚠️ Profile already exists locally:', profilePath);
    throw new Error('Profile đã được tải xuống rồi');
  } else {
    // Profile chưa tồn tại, download từ server
    console.log('⬇️ Downloading profile from server...');
    const { browser } = await api.launch({
      profileId,
      localStorageDir: folderDirectory
      // Không truyền local: true vì muốn download từ server
    });
    console.log('✅ Profile downloaded and launched:', profilePath);
    return { success: true, profilePath, browser, fromLocal: false };
  }
}

// Export cho ES modules
export {
  launchBrowserFromLocal,
  getLocalProfiles,
  closeBrowser,
  createDownloadAndOpenProfile,
  downloadProfileFromServer
};
